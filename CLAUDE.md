# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Overview

This appears to be a decompiled/extracted Android APK for ShopeePay Merchant Vietnam app (com.shopeepay.merchant.vn, version 3.23.0). The codebase contains extracted resources and decompiled Java sources rather than original development source code.

## Code Architecture and Structure

### Directory Structure
- **resources/**: Contains all Android resources, assets, and native libraries
  - `AndroidManifest.xml`: Main Android manifest file
  - `assets/`: Application assets including configuration files, models, and web content
  - `lib/`: Native libraries (.so files) for different architectures (arm64-v8a, armeabi-v7a, x86, x86_64)
  - `META-INF/`: APK signing and metadata information
  - Various resource directories (drawable, layout, values, etc.)

- **sources/**: Decompiled Java source files organized by package
  - Contains obfuscated code (single-letter class names like a.java, b.java, etc.)
  - Major packages include: com/, org/, kotlin/, kotlinx/, retrofit2/, okhttp3/, butterknife/

### Key Technologies
- **Android Native**: Core Android SDK components
- **React Native**: Hybrid mobile development framework
- **Kotlin**: Modern Android programming language
- **Firebase**: Analytics, messaging, crash reporting
- **OkHttp/Retrofit**: Networking libraries
- **RxJava**: Reactive programming
- **Protobuf**: Data serialization

### Important Notes
1. **Decompiled Code**: Most Java files are decompiled and obfuscated, making them difficult to understand
2. **No Build System**: No Gradle files or build scripts are present - this is not a buildable project
3. **Native Libraries**: Contains numerous native libraries for features like:
   - Image processing (libimagepipeline.so)
   - Media handling (libmediautils.so)
   - Security (libshopeeaegis.so)
   - React Native runtime (libreactnativejni.so)

## Commands

Since this is a decompiled APK rather than a development project, there are no build, test, or lint commands available. The codebase can only be examined and analyzed, not built or executed.

## Working with This Codebase

When analyzing this codebase:
1. Focus on understanding the app structure through AndroidManifest.xml
2. Examine resource files for configuration and assets
3. Be aware that Java source files are decompiled and may not represent original code structure
4. Native libraries indicate feature capabilities but cannot be modified
5. Configuration files in assets/ may provide insights into app behavior

## Security Considerations

This appears to be a financial/payment application. Exercise caution when:
- Examining any code related to payment processing
- Looking at authentication or security implementations
- Analyzing any encrypted or obfuscated content