# Shopee FCM Token Sender

This project provides tools to send FCM (Firebase Cloud Messaging) tokens to Shopee servers using the protocol analysis from the Android app codebase. It includes both a Node.js implementation and a Frida script for extracting additional information from the mobile app.

## Files

- `shopee-fcm-sender.js` - Node.js script to send FCM tokens to Shopee servers
- `shopee-frida-extractor.js` - <PERSON><PERSON> script to extract FCM token and device information from the Shopee Android app
- `README.md` - This documentation file

## Prerequisites

### For Node.js Script
- Node.js (v14 or higher)
- Network access to Shopee servers

### For Frida Script
- Frida installed (`pip install frida-tools`)
- Android device with Shopee app installed
- USB debugging enabled
- Root access (recommended for better hooking)

## Usage

### 1. Using the Node.js Script

The Node.js script can send FCM tokens directly to Shopee servers using the reverse-engineered protocol.

#### Basic Usage
```bash
node shopee-fcm-sender.js --token "your_fcm_token_here"
```

#### Advanced Usage
```bash
# With custom device ID and user ID
node shopee-fcm-sender.js --token "fcm_token" --device-id "custom_device_id" --user-id 12345

# Using SSL connection
node shopee-fcm-sender.js --token "fcm_token" --ssl

# Using legacy protocol instead of magic protocol
node shopee-fcm-sender.js --token "fcm_token" --legacy

# With custom timeout
node shopee-fcm-sender.js --token "fcm_token" --timeout 60000
```

#### Command Line Options
- `--token <fcm_token>` - FCM token to register (required)
- `--device-id <id>` - Custom device ID (optional, auto-generated if not provided)
- `--user-id <id>` - User ID (optional, default: 0)
- `--ssl` - Use SSL connection (port 20443 instead of 20346)
- `--legacy` - Use legacy protocol instead of magic protocol
- `--timeout <ms>` - Connection timeout in milliseconds (default: 30000)
- `--help, -h` - Show help message

### 2. Using the Frida Script

The Frida script hooks into the Shopee Android app to extract real FCM tokens, device information, and network communication patterns.

#### Basic Usage
```bash
# Connect to USB device and launch Shopee app
frida -U -f com.shopee.vn -l shopee-frida-extractor.js --no-pause

# Connect to running Shopee app
frida -U com.shopee.vn -l shopee-frida-extractor.js
```

#### What the Frida Script Captures
- FCM token generation and updates
- Device ID and fingerprint
- Network packet details
- Protobuf message structure
- TCP connection information
- User agent strings
- Base64 encoded data

### 3. Combined Workflow

For the most effective approach, use both tools together:

1. **Extract real data with Frida:**
   ```bash
   frida -U -f com.shopee.vn -l shopee-frida-extractor.js --no-pause
   ```

2. **Trigger FCM token registration in the app** (e.g., by logging in/out or clearing app data)

3. **Copy the extracted information** from Frida output

4. **Use the Node.js script with real data:**
   ```bash
   node shopee-fcm-sender.js --token "extracted_fcm_token" --device-id "extracted_device_id"
   ```

## Protocol Details

Based on the codebase analysis, the implementation uses:

### Server Configuration
- **Host:** `partner.api.shopee.vn`
- **Port:** `20346` (TCP) or `20443` (SSL)
- **Command:** `158` (CMD_REGISTER_DEVICE)

### Message Structure
The protocol uses a custom packet format with:
1. **Packet Header:** 4-byte length + 1-byte command
2. **Magic Protocol Header:** MessageHeader protobuf structure
3. **Payload:** RegisterDevice protobuf message containing Device information

### Device Information
- Device ID (Base64 encoded)
- FCM Token (UTF-8 encoded)
- Machine Code (e.g., "android_fcm")
- App Version (32300)
- Country Code ("VN")
- User ID
- Device Fingerprint
- User Agent

## Security Considerations

- This tool is for educational and research purposes
- Ensure you have permission to test against Shopee servers
- Use responsibly and in compliance with terms of service
- Consider rate limiting to avoid overwhelming servers

## Troubleshooting

### Common Issues

1. **Connection Timeout**
   - Check network connectivity
   - Try using SSL mode with `--ssl`
   - Increase timeout with `--timeout 60000`

2. **Frida Script Not Working**
   - Ensure Shopee app is the correct version
   - Try with root access
   - Check if app is obfuscated/protected

3. **Invalid Response**
   - Verify FCM token format
   - Check device ID encoding
   - Ensure all required fields are present

### Debug Mode

For debugging, you can modify the Node.js script to add more verbose logging:

```javascript
// Add this near the top of shopee-fcm-sender.js
const DEBUG = true;

// Then add debug logs throughout the code
if (DEBUG) {
    console.log('Debug info:', data);
}
```

## Contributing

Feel free to contribute improvements:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Disclaimer

This project is for educational and research purposes only. The authors are not responsible for any misuse of this code. Always ensure you have proper authorization before testing against any servers.

## License

This project is provided as-is for educational purposes. Use at your own risk and in compliance with applicable laws and terms of service.
