/**
 * Node.js implementation to send FCM token to Shopee server
 * Based on reverse-engineered protocol from Shopee Merchant app
 */

const net = require('net');
const protobuf = require('protobufjs');
const crypto = require('crypto');

// Shopee server configuration
const SHOPEE_CONFIG = {
  host: 'partner.api.shopee.vn',
  port: 20346,
  sslPort: 20443,
  timeout: 30000,
  country: 'VN'
};

// Command IDs from Shopee protocol
const Commands = {
  CMD_SET_USER_INFO: 67,
  CMD_REGISTER_DEVICE: 158,
  CMD_LOGIN: 3
};

// Create protobuf schema for SetUserInfo message
const setUserInfoProto = `
syntax = "proto3";

message SetUserInfo {
  string pn_token = 1;
  string machine_code = 2;
  string deviceid = 3;
  string country = 4;
  int32 appversion = 5;
  string language = 6;
  string ext = 7;
  string requestid = 8;
}

message RegisterDevice {
  string deviceid = 1;
  bytes pn_token = 2;
  string machine_code = 3;
  string requestid = 4;
}
`;

class ShopeeClient {
  constructor() {
    this.socket = null;
    this.root = protobuf.parse(setUserInfoProto).root;
    this.SetUserInfo = this.root.lookupType('SetUserInfo');
    this.RegisterDevice = this.root.lookupType('RegisterDevice');
  }

  /**
   * Generate device ID similar to Shopee app
   */
  generateDeviceId() {
    // Shopee uses Android ID or a random UUID
    const androidId = crypto.randomBytes(8).toString('hex');
    return Buffer.from(androidId).toString('base64');
  }

  /**
   * Generate request ID
   */
  generateRequestId() {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Create packet with Shopee protocol format
   * @param {number} commandId - Command ID
   * @param {Buffer} data - Protobuf encoded data
   * @returns {Buffer} Complete packet
   */
  createPacket(commandId, data) {
    // Packet structure:
    // 4 bytes: length (excluding length field)
    // 1 byte: command ID
    // N bytes: data
    
    const packetLength = 1 + data.length; // command ID + data
    const packet = Buffer.allocUnsafe(4 + packetLength);
    
    // Write length (big endian)
    packet.writeInt32BE(packetLength, 0);
    
    // Write command ID
    packet.writeUInt8(commandId, 4);
    
    // Write data
    data.copy(packet, 5);
    
    return packet;
  }

  /**
   * Connect to Shopee server
   */
  connect() {
    return new Promise((resolve, reject) => {
      this.socket = new net.Socket();
      
      this.socket.setTimeout(SHOPEE_CONFIG.timeout);
      
      this.socket.connect(SHOPEE_CONFIG.port, SHOPEE_CONFIG.host, () => {
        console.log('Connected to Shopee server');
        resolve();
      });

      this.socket.on('error', (err) => {
        console.error('Connection error:', err);
        reject(err);
      });

      this.socket.on('timeout', () => {
        console.error('Connection timeout');
        this.socket.destroy();
        reject(new Error('Connection timeout'));
      });

      this.socket.on('data', (data) => {
        console.log('Received data from server:', data.toString('hex'));
        // In production, you would parse the response here
      });
    });
  }

  /**
   * Send FCM token for logged-in user
   * @param {Object} options - Configuration options
   * @param {string} options.fcmToken - Firebase Cloud Messaging token
   * @param {string} options.deviceId - Device ID (optional)
   * @param {string} options.language - Language code (default: 'en')
   * @param {number} options.appVersion - App version (default: 32300)
   */
  async sendFcmTokenLoggedIn(options) {
    const {
      fcmToken,
      deviceId = this.generateDeviceId(),
      language = 'en',
      appVersion = 32300
    } = options;

    // Create device extension info
    const deviceExt = {
      fingerprint: 'generic/sdk_gphone64_arm64/emu64a:13/TE1A.220922.034/9278194:userdebug/dev-keys',
      useragent: 'Mozilla/5.0 (Linux; Android 13; sdk_gphone64_arm64)',
      root: false
    };

    // Create SetUserInfo message
    const message = {
      pn_token: fcmToken,
      machine_code: 'android_gcm',
      deviceid: deviceId,
      country: SHOPEE_CONFIG.country,
      appversion: appVersion,
      language: language,
      ext: JSON.stringify(deviceExt),
      requestid: this.generateRequestId()
    };

    // Verify and encode message
    const errMsg = this.SetUserInfo.verify(message);
    if (errMsg) throw Error(errMsg);

    const setUserInfoMessage = this.SetUserInfo.create(message);
    const buffer = this.SetUserInfo.encode(setUserInfoMessage).finish();

    // Create packet
    const packet = this.createPacket(Commands.CMD_SET_USER_INFO, buffer);

    // Send packet
    await this.connect();
    
    return new Promise((resolve, reject) => {
      this.socket.write(packet, (err) => {
        if (err) {
          console.error('Failed to send packet:', err);
          reject(err);
        } else {
          console.log('FCM token sent successfully');
          console.log('Token:', fcmToken);
          console.log('Device ID:', deviceId);
          resolve({ success: true, deviceId, requestId: message.requestid });
        }
      });

      // Wait for response (simplified - in production you'd parse the response)
      setTimeout(() => {
        this.socket.end();
        resolve({ success: true, deviceId, requestId: message.requestid });
      }, 2000);
    });
  }

  /**
   * Send FCM token for non-logged-in user
   * @param {Object} options - Configuration options
   * @param {string} options.fcmToken - Firebase Cloud Messaging token
   * @param {string} options.deviceId - Device ID (optional)
   */
  async sendFcmTokenAnonymous(options) {
    const {
      fcmToken,
      deviceId = this.generateDeviceId()
    } = options;

    // Create RegisterDevice message
    const message = {
      deviceid: deviceId,
      pn_token: Buffer.from(fcmToken, 'utf8'),
      machine_code: 'android_gcm',
      requestid: this.generateRequestId()
    };

    // Verify and encode message
    const errMsg = this.RegisterDevice.verify(message);
    if (errMsg) throw Error(errMsg);

    const registerDeviceMessage = this.RegisterDevice.create(message);
    const buffer = this.RegisterDevice.encode(registerDeviceMessage).finish();

    // Create packet
    const packet = this.createPacket(Commands.CMD_REGISTER_DEVICE, buffer);

    // Send packet
    await this.connect();
    
    return new Promise((resolve, reject) => {
      this.socket.write(packet, (err) => {
        if (err) {
          console.error('Failed to send packet:', err);
          reject(err);
        } else {
          console.log('FCM token sent successfully (anonymous)');
          console.log('Token:', fcmToken);
          console.log('Device ID:', deviceId);
          resolve({ success: true, deviceId, requestId: message.requestid });
        }
      });

      // Wait for response
      setTimeout(() => {
        this.socket.end();
        resolve({ success: true, deviceId, requestId: message.requestid });
      }, 2000);
    });
  }

  /**
   * Close connection
   */
  close() {
    if (this.socket) {
      this.socket.end();
      this.socket.destroy();
    }
  }
}

// Example usage
async function main() {
  const client = new ShopeeClient();
  
  try {
    // Example FCM token (you would get this from Firebase)
    const fcmToken = 'YOUR_FCM_TOKEN_HERE';
    
    // Send token for logged-in user
    console.log('Sending FCM token for logged-in user...');
    const result = await client.sendFcmTokenLoggedIn({
      fcmToken: fcmToken,
      language: 'vi',
      appVersion: 32300
    });
    console.log('Result:', result);
    
    // Alternatively, send token for anonymous user
    // const result = await client.sendFcmTokenAnonymous({
    //   fcmToken: fcmToken
    // });
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    client.close();
  }
}

// Run if executed directly
if (require.main === module) {
  main();
}

module.exports = ShopeeClient;

/**
 * Installation:
 * npm install protobufjs
 * 
 * Usage:
 * 1. Replace 'YOUR_FCM_TOKEN_HERE' with actual FCM token
 * 2. Run: node send_fcm_to_shopee.js
 * 
 * Note: This implementation is based on reverse engineering and may need adjustments
 * based on actual server responses and authentication requirements.
 */