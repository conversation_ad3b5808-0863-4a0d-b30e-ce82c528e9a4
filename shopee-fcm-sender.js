#!/usr/bin/env node

/**
 * Shopee FCM Token Sender
 * 
 * This Node.js script sends FCM tokens to Shopee servers using the protocol
 * analysis from the Android app codebase. It implements the RegisterDevice
 * protocol message structure used by the app.
 * 
 * Usage: node shopee-fcm-sender.js [options]
 */

const net = require('net');
const crypto = require('crypto');
const { Buffer } = require('buffer');

// Configuration based on codebase analysis
const CONFIG = {
    // Server endpoints from codebase
    SERVER_HOST: 'partner.api.shopee.vn',
    SERVER_PORT: 20346,
    SSL_PORT: 20443,
    
    // Protocol constants
    CMD_REGISTER_DEVICE: 158,
    APP_VERSION: 32300,
    COUNTRY: 'VN',
    PLATFORM: 1, // Android
    
    // Message types
    MESSAGE_TYPE_REQUEST: 1,
    APP_TYPE: 1,
    
    // Timeout
    DEFAULT_TIMEOUT: 30000
};

class ShopeeProtocolBuffer {
    constructor() {
        this.buffer = Buffer.alloc(0);
    }
    
    writeInt32(value) {
        const buf = Buffer.allocUnsafe(4);
        buf.writeInt32LE(value, 0);
        this.buffer = Buffer.concat([this.buffer, buf]);
    }
    
    writeString(str) {
        if (!str) str = '';
        const strBuf = Buffer.from(str, 'utf8');
        this.writeInt32(strBuf.length);
        this.buffer = Buffer.concat([this.buffer, strBuf]);
    }
    
    writeByteString(data) {
        if (!data) data = Buffer.alloc(0);
        this.writeInt32(data.length);
        this.buffer = Buffer.concat([this.buffer, data]);
    }
    
    writeBool(value) {
        const buf = Buffer.allocUnsafe(1);
        buf.writeUInt8(value ? 1 : 0, 0);
        this.buffer = Buffer.concat([this.buffer, buf]);
    }
    
    toBuffer() {
        return this.buffer;
    }
}

class ShopeeDeviceInfo {
    constructor(options = {}) {
        this.deviceId = options.deviceId || this.generateDeviceId();
        this.fcmToken = options.fcmToken || '';
        this.machineCode = options.machineCode || 'android_fcm';
        this.userId = options.userId || 0;
        this.deviceFingerprint = options.deviceFingerprint || this.generateFingerprint();
        this.userAgent = options.userAgent || this.generateUserAgent();
    }
    
    generateDeviceId() {
        // Generate a device ID similar to Android's approach
        return crypto.randomBytes(16).toString('base64');
    }
    
    generateFingerprint() {
        // Generate device fingerprint
        return crypto.randomBytes(32);
    }
    
    generateUserAgent() {
        return 'Shopee/32300 (Android 11; SM-G973F)';
    }
    
    generateRequestId() {
        return crypto.randomUUID();
    }
}

class ShopeePacketBuilder {
    static buildRegisterDevicePacket(deviceInfo, fcmToken) {
        const requestId = deviceInfo.generateRequestId();
        
        // Build Device message (protobuf-like structure)
        const deviceBuffer = new ShopeeProtocolBuffer();
        
        // Device fields based on Device.java structure
        deviceBuffer.writeByteString(Buffer.from(deviceInfo.deviceId, 'base64')); // deviceid
        deviceBuffer.writeByteString(Buffer.from(fcmToken, 'utf8')); // pn_token
        deviceBuffer.writeInt32(Math.floor(Date.now() / 1000)); // ctime
        deviceBuffer.writeInt32(Math.floor(Date.now() / 1000)); // mtime
        deviceBuffer.writeInt32(0); // device_hash
        deviceBuffer.writeInt32(CONFIG.APP_VERSION); // appversion
        deviceBuffer.writeString(CONFIG.COUNTRY); // country
        deviceBuffer.writeString(deviceInfo.machineCode); // machine_code
        deviceBuffer.writeByteString(deviceInfo.deviceFingerprint); // extinfo
        deviceBuffer.writeInt32(deviceInfo.userId); // userid
        deviceBuffer.writeInt32(0); // id
        
        // Build RegisterDevice message
        const registerBuffer = new ShopeeProtocolBuffer();
        registerBuffer.writeString(requestId); // requestid
        registerBuffer.writeByteString(deviceBuffer.toBuffer()); // device
        
        return {
            requestId,
            payload: registerBuffer.toBuffer()
        };
    }
    
    static buildTCPPacket(command, payload) {
        const totalLength = payload.length + 5;
        const packet = Buffer.allocUnsafe(totalLength);
        
        // Write length (4 bytes, little endian)
        packet.writeInt32LE(payload.length + 1, 0);
        
        // Write command (1 byte)
        packet.writeUInt8(command, 4);
        
        // Write payload
        payload.copy(packet, 5);
        
        return packet;
    }
    
    static buildMagicPacket(requestId, command, payload, timeout = CONFIG.DEFAULT_TIMEOUT) {
        // Build MessageHeader for Magic protocol
        const headerBuffer = new ShopeeProtocolBuffer();
        headerBuffer.writeBool(false); // extended_service
        headerBuffer.writeInt32(0); // service_id
        headerBuffer.writeInt32(command); // cmd
        headerBuffer.writeBool(false); // compression_aware
        headerBuffer.writeBool(false); // compressed
        headerBuffer.writeInt32(CONFIG.MESSAGE_TYPE_REQUEST); // message_type
        headerBuffer.writeInt32(CONFIG.APP_TYPE); // app_type
        headerBuffer.writeInt32(timeout); // timeout
        headerBuffer.writeInt32(0); // stream_frame_id
        headerBuffer.writeString(requestId); // request_id
        headerBuffer.writeString(''); // source
        
        // Combine header and payload
        const header = headerBuffer.toBuffer();
        const totalPayload = Buffer.concat([header, payload]);
        
        return this.buildTCPPacket(command, totalPayload);
    }
}

class ShopeeFCMSender {
    constructor(options = {}) {
        this.deviceInfo = new ShopeeDeviceInfo(options);
        this.useSSL = options.useSSL || false;
        this.useMagicProtocol = options.useMagicProtocol !== false; // Default to true
        this.timeout = options.timeout || CONFIG.DEFAULT_TIMEOUT;
    }
    
    async sendFCMToken(fcmToken) {
        return new Promise((resolve, reject) => {
            const port = this.useSSL ? CONFIG.SSL_PORT : CONFIG.SERVER_PORT;
            const socket = net.createConnection(port, CONFIG.SERVER_HOST);
            
            let responseBuffer = Buffer.alloc(0);
            let timeoutId;
            
            const cleanup = () => {
                if (timeoutId) clearTimeout(timeoutId);
                socket.destroy();
            };
            
            timeoutId = setTimeout(() => {
                cleanup();
                reject(new Error('Connection timeout'));
            }, this.timeout);
            
            socket.on('connect', () => {
                console.log(`Connected to ${CONFIG.SERVER_HOST}:${port}`);
                
                try {
                    const { requestId, payload } = ShopeePacketBuilder.buildRegisterDevicePacket(
                        this.deviceInfo, 
                        fcmToken
                    );
                    
                    let packet;
                    if (this.useMagicProtocol) {
                        packet = ShopeePacketBuilder.buildMagicPacket(
                            requestId, 
                            CONFIG.CMD_REGISTER_DEVICE, 
                            payload
                        );
                    } else {
                        packet = ShopeePacketBuilder.buildTCPPacket(
                            CONFIG.CMD_REGISTER_DEVICE, 
                            payload
                        );
                    }
                    
                    console.log(`Sending FCM token registration...`);
                    console.log(`Request ID: ${requestId}`);
                    console.log(`FCM Token: ${fcmToken}`);
                    console.log(`Device ID: ${this.deviceInfo.deviceId}`);
                    console.log(`Packet size: ${packet.length} bytes`);
                    
                    socket.write(packet);
                } catch (error) {
                    cleanup();
                    reject(error);
                }
            });
            
            socket.on('data', (data) => {
                responseBuffer = Buffer.concat([responseBuffer, data]);
                
                // Try to parse response (simplified)
                if (responseBuffer.length >= 4) {
                    const responseLength = responseBuffer.readInt32LE(0);
                    if (responseBuffer.length >= responseLength + 4) {
                        console.log('Received response from server');
                        console.log(`Response length: ${responseLength}`);
                        console.log(`Response data: ${responseBuffer.slice(4).toString('hex')}`);
                        
                        cleanup();
                        resolve({
                            success: true,
                            responseLength,
                            responseData: responseBuffer.slice(4)
                        });
                    }
                }
            });
            
            socket.on('error', (error) => {
                cleanup();
                reject(error);
            });
            
            socket.on('close', () => {
                if (timeoutId) {
                    cleanup();
                    reject(new Error('Connection closed unexpectedly'));
                }
            });
        });
    }
}

// CLI Interface
async function main() {
    const args = process.argv.slice(2);
    
    if (args.includes('--help') || args.includes('-h')) {
        console.log(`
Shopee FCM Token Sender

Usage: node shopee-fcm-sender.js [options]

Options:
  --token <fcm_token>     FCM token to register (required)
  --device-id <id>        Custom device ID (optional)
  --user-id <id>          User ID (optional, default: 0)
  --ssl                   Use SSL connection
  --legacy                Use legacy protocol instead of magic protocol
  --timeout <ms>          Connection timeout in milliseconds (default: 30000)
  --help, -h              Show this help message

Examples:
  node shopee-fcm-sender.js --token "your_fcm_token_here"
  node shopee-fcm-sender.js --token "token" --ssl --user-id 12345
        `);
        return;
    }
    
    const tokenIndex = args.indexOf('--token');
    if (tokenIndex === -1 || !args[tokenIndex + 1]) {
        console.error('Error: FCM token is required. Use --token <fcm_token>');
        process.exit(1);
    }
    
    const fcmToken = args[tokenIndex + 1];
    
    const options = {
        useSSL: args.includes('--ssl'),
        useMagicProtocol: !args.includes('--legacy'),
        timeout: parseInt(args[args.indexOf('--timeout') + 1]) || CONFIG.DEFAULT_TIMEOUT
    };
    
    const deviceIdIndex = args.indexOf('--device-id');
    if (deviceIdIndex !== -1 && args[deviceIdIndex + 1]) {
        options.deviceId = args[deviceIdIndex + 1];
    }
    
    const userIdIndex = args.indexOf('--user-id');
    if (userIdIndex !== -1 && args[userIdIndex + 1]) {
        options.userId = parseInt(args[userIdIndex + 1]) || 0;
    }
    
    try {
        const sender = new ShopeeFCMSender(options);
        const result = await sender.sendFCMToken(fcmToken);
        
        console.log('\n✅ FCM token sent successfully!');
        console.log('Response:', result);
    } catch (error) {
        console.error('\n❌ Failed to send FCM token:');
        console.error(error.message);
        process.exit(1);
    }
}

// Export for use as module
module.exports = {
    ShopeeFCMSender,
    ShopeeDeviceInfo,
    ShopeePacketBuilder,
    CONFIG
};

// Run CLI if called directly
if (require.main === module) {
    main().catch(console.error);
}
