/**
 * Shopee FCM Token Extraction Frida Script
 * 
 * This Frida script hooks into the Shopee Android app to extract FCM token
 * registration details, device information, and network communication patterns.
 * 
 * Usage: frida -U -f com.shopee.vn -l shopee-frida-extractor.js --no-pause
 */

console.log("[*] Shopee FCM Token Extractor - Starting...");

// Helper function to convert byte array to hex string
function bytesToHex(bytes) {
    if (!bytes) return "null";
    let hex = "";
    for (let i = 0; i < bytes.length; i++) {
        hex += ("0" + (bytes[i] & 0xFF).toString(16)).slice(-2);
    }
    return hex;
}

// Helper function to convert byte array to string
function bytesToString(bytes) {
    if (!bytes) return "null";
    try {
        return Java.use("java.lang.String").$new(bytes, "UTF-8");
    } catch (e) {
        return bytesToHex(bytes);
    }
}

Java.perform(function() {
    console.log("[*] Java environment ready");
    
    // Hook FCM token generation and handling
    try {
        const ShopeeFcmMessageService = Java.use("com.shopee.app.pushnotification.fcm.ShopeeFcmMessageService");
        
        ShopeeFcmMessageService.onNewToken.implementation = function(token) {
            console.log("\n[+] FCM Token Generated:");
            console.log("    Token: " + token);
            console.log("    Length: " + token.length);
            console.log("    Timestamp: " + new Date().toISOString());
            
            // Call original method
            return this.onNewToken(token);
        };
        
        console.log("[*] Hooked ShopeeFcmMessageService.onNewToken");
    } catch (e) {
        console.log("[-] Failed to hook ShopeeFcmMessageService: " + e);
    }
    
    // Hook device registration request
    try {
        const RegisterDeviceRequest = Java.use("com.shopee.app.network.request.login.k");
        
        RegisterDeviceRequest.d.implementation = function() {
            console.log("\n[+] RegisterDevice Request:");
            console.log("    Device ID (b): " + this.b.value);
            console.log("    FCM Token (c): " + bytesToString(this.c.value.toByteArray()));
            console.log("    Machine Code (d): " + this.d.value);
            
            const result = this.d();
            console.log("    Command: " + result.b());
            console.log("    Payload Length: " + result.c().length);
            console.log("    Payload Hex: " + bytesToHex(result.c()));
            
            return result;
        };
        
        console.log("[*] Hooked RegisterDevice request builder");
    } catch (e) {
        console.log("[-] Failed to hook RegisterDevice: " + e);
    }
    
    // Hook device information
    try {
        const DeviceStore = Java.use("com.shopee.app.manager.j0");
        
        // Hook device ID getter
        DeviceStore.k.implementation = function() {
            const deviceId = this.k();
            console.log("\n[+] Device ID accessed: " + deviceId);
            return deviceId;
        };
        
        // Hook device fingerprint getter
        DeviceStore.m.implementation = function() {
            const fingerprint = this.m();
            console.log("[+] Device Fingerprint: " + bytesToHex(fingerprint));
            return fingerprint;
        };
        
        console.log("[*] Hooked DeviceStore methods");
    } catch (e) {
        console.log("[-] Failed to hook DeviceStore: " + e);
    }
    
    // Hook TCP packet creation
    try {
        const PacketManager = Java.use("com.shopee.app.network.tcp.manager.PacketManager");
        
        PacketManager.b.implementation = function(requestId, message, packet, num) {
            console.log("\n[+] TCP Packet Created:");
            console.log("    Request ID: " + requestId);
            console.log("    Command: " + packet.b());
            console.log("    Payload Length: " + packet.c().length);
            console.log("    Payload Preview: " + bytesToHex(packet.c().slice(0, 32)));
            
            if (num) {
                console.log("    Timeout: " + num);
            }
            
            return this.b(requestId, message, packet, num);
        };
        
        console.log("[*] Hooked PacketManager.b");
    } catch (e) {
        console.log("[-] Failed to hook PacketManager: " + e);
    }
    
    // Hook network manager for server communication
    try {
        const NetworkManager = Java.use("com.shopee.app.network.e");
        
        NetworkManager.p.implementation = function(packet, str, str2, message) {
            console.log("\n[+] Network Request:");
            console.log("    Command: " + packet.b());
            console.log("    Request ID: " + str2);
            console.log("    Callback: " + str);
            console.log("    Payload Size: " + packet.c().length);
            
            return this.p(packet, str, str2, message);
        };
        
        console.log("[*] Hooked NetworkManager.p");
    } catch (e) {
        console.log("[-] Failed to hook NetworkManager: " + e);
    }
    
    // Hook message service handler
    try {
        const MessageServiceHandler = Java.use("com.shopee.app.pushnotification.b");
        
        MessageServiceHandler.h.implementation = function(token) {
            console.log("\n[+] FCM Token Handler:");
            console.log("    Token: " + token);
            console.log("    Handler: MessageServiceHandler.h");
            
            return this.h(token);
        };
        
        MessageServiceHandler.j.implementation = function(token) {
            console.log("\n[+] Send Registration to Server:");
            console.log("    Token: " + token);
            
            return this.j(token);
        };
        
        console.log("[*] Hooked MessageServiceHandler methods");
    } catch (e) {
        console.log("[-] Failed to hook MessageServiceHandler: " + e);
    }
    
    // Hook protobuf message building
    try {
        const RegisterDevice = Java.use("com.shopee.protocol.action.RegisterDevice");
        const Device = Java.use("com.shopee.protocol.shop.Device");
        
        // Hook Device.Builder
        const DeviceBuilder = Java.use("com.shopee.protocol.shop.Device$Builder");
        
        DeviceBuilder.build.implementation = function() {
            console.log("\n[+] Device Protobuf Message:");
            
            if (this.deviceid.value) {
                console.log("    Device ID: " + bytesToHex(this.deviceid.value.toByteArray()));
            }
            if (this.pn_token.value) {
                console.log("    FCM Token: " + bytesToString(this.pn_token.value.toByteArray()));
            }
            if (this.country.value) {
                console.log("    Country: " + this.country.value);
            }
            if (this.machine_code.value) {
                console.log("    Machine Code: " + this.machine_code.value);
            }
            if (this.appversion.value) {
                console.log("    App Version: " + this.appversion.value);
            }
            if (this.userid.value) {
                console.log("    User ID: " + this.userid.value);
            }
            
            return this.build();
        };
        
        console.log("[*] Hooked Device.Builder.build");
    } catch (e) {
        console.log("[-] Failed to hook Device protobuf: " + e);
    }
    
    // Hook TCP connection details
    try {
        const TCPConnection = Java.use("com.garena.tcpcore.tcp.a");
        
        TCPConnection.$init.overload('java.lang.String', 'java.lang.String', 'int').implementation = function(name, host, port) {
            console.log("\n[+] TCP Connection:");
            console.log("    Name: " + name);
            console.log("    Host: " + host);
            console.log("    Port: " + port);
            
            return this.$init(name, host, port);
        };
        
        console.log("[*] Hooked TCP connection constructor");
    } catch (e) {
        console.log("[-] Failed to hook TCP connection: " + e);
    }
    
    // Hook SSL connection if used
    try {
        const TCPConnectionSSL = Java.use("com.garena.tcpcore.tcp.a");
        
        TCPConnectionSSL.$init.overload('java.lang.String', 'java.lang.String', 'int', 'java.net.Socket').implementation = function(name, host, port, socket) {
            console.log("\n[+] SSL TCP Connection:");
            console.log("    Name: " + name);
            console.log("    Host: " + host);
            console.log("    Port: " + port);
            console.log("    Socket: " + socket);
            
            return this.$init(name, host, port, socket);
        };
        
        console.log("[*] Hooked SSL TCP connection constructor");
    } catch (e) {
        console.log("[-] Failed to hook SSL TCP connection: " + e);
    }
    
    // Hook user agent generation
    try {
        const UserAgentUtil = Java.use("com.shopee.app.util.k");
        
        UserAgentUtil.c.implementation = function() {
            const userAgent = this.c();
            console.log("\n[+] User Agent: " + userAgent);
            return userAgent;
        };
        
        console.log("[*] Hooked User Agent generation");
    } catch (e) {
        console.log("[-] Failed to hook User Agent: " + e);
    }
    
    // Hook Base64 operations for device ID
    try {
        const Base64 = Java.use("android.util.Base64");
        
        Base64.decode.overload('java.lang.String', 'int').implementation = function(str, flags) {
            const result = this.decode(str, flags);
            
            if (str.length > 10) { // Likely device ID
                console.log("\n[+] Base64 Decode:");
                console.log("    Input: " + str);
                console.log("    Output Hex: " + bytesToHex(result));
                console.log("    Flags: " + flags);
            }
            
            return result;
        };
        
        console.log("[*] Hooked Base64.decode");
    } catch (e) {
        console.log("[-] Failed to hook Base64: " + e);
    }
    
    console.log("\n[*] All hooks installed. Monitoring FCM token registration...");
    console.log("[*] Trigger FCM token registration in the app to see the data.");
});

// Hook native socket operations if needed
Interceptor.attach(Module.findExportByName("libc.so", "connect"), {
    onEnter: function(args) {
        const sockfd = args[0].toInt32();
        const addr = args[1];
        const addrlen = args[2].toInt32();
        
        if (addrlen === 16) { // IPv4 sockaddr_in
            const family = addr.readU16();
            if (family === 2) { // AF_INET
                const port = (addr.add(2).readU16() << 8) | (addr.add(2).readU16() >> 8);
                const ip = addr.add(4).readU32();
                const ipStr = [
                    (ip & 0xFF),
                    ((ip >> 8) & 0xFF),
                    ((ip >> 16) & 0xFF),
                    ((ip >> 24) & 0xFF)
                ].join('.');
                
                if (port === 20346 || port === 20443) {
                    console.log("\n[+] Native Socket Connection:");
                    console.log("    IP: " + ipStr);
                    console.log("    Port: " + port);
                    console.log("    Socket FD: " + sockfd);
                }
            }
        }
    }
});

console.log("[*] Native socket hooks installed");
